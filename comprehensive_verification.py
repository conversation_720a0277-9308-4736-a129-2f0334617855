#!/usr/bin/env python3
"""
全面验证 version-0.72-sidebars.json 中的文件与实际目录的匹配情况
"""

import json
from pathlib import Path

def extract_doc_ids_from_sidebar(sidebar_data, doc_ids=None):
    """递归提取 sidebar 中的所有文档 ID"""
    if doc_ids is None:
        doc_ids = set()
    
    if isinstance(sidebar_data, str):
        doc_ids.add(sidebar_data)
    elif isinstance(sidebar_data, list):
        for item in sidebar_data:
            extract_doc_ids_from_sidebar(item, doc_ids)
    elif isinstance(sidebar_data, dict):
        if 'items' in sidebar_data:
            extract_doc_ids_from_sidebar(sidebar_data['items'], doc_ids)
        else:
            for value in sidebar_data.values():
                extract_doc_ids_from_sidebar(value, doc_ids)
    
    return doc_ids

def main():
    # 读取 sidebar 文件
    sidebar_path = "website/versioned_sidebars/version-0.72-sidebars.json"
    
    with open(sidebar_path, 'r', encoding='utf-8') as f:
        sidebar_data = json.load(f)
    
    # 提取所有文档 ID
    sidebar_doc_ids = extract_doc_ids_from_sidebar(sidebar_data)
    
    print(f"📋 sidebar 中定义的文档 ID 数量: {len(sidebar_doc_ids)}")
    print("所有文档 ID:")
    for doc_id in sorted(sidebar_doc_ids):
        print(f"  {doc_id}")
    
    # 检查 version-0.72 目录中的文件
    version_072_dir = Path("website/versioned_docs/version-0.72")
    version_072_files = {}
    
    for file_path in version_072_dir.rglob("*"):
        if file_path.is_file() and file_path.suffix in ['.md', '.mdx']:
            # 提取文档 ID（去掉扩展名和路径前缀）
            rel_path = file_path.relative_to(version_072_dir)
            if rel_path.name.startswith('_'):
                # 跳过以下划线开头的文件，这些是特殊文件
                continue
            doc_id = str(rel_path).replace('.md', '').replace('.mdx', '')
            version_072_files[doc_id] = str(file_path)
    
    print(f"\n📁 version-0.72 目录中的文档文件数量: {len(version_072_files)}")
    
    # 检查 docs 目录中的文件
    docs_dir = Path("docs")
    docs_files = {}
    
    for file_path in docs_dir.rglob("*"):
        if file_path.is_file() and file_path.suffix in ['.md', '.mdx']:
            # 提取文档 ID
            rel_path = file_path.relative_to(docs_dir)
            if rel_path.name.startswith('_'):
                # 跳过以下划线开头的文件
                continue
            doc_id = str(rel_path).replace('.md', '').replace('.mdx', '')
            docs_files[doc_id] = str(file_path)
    
    print(f"📁 docs 目录中的文档文件数量: {len(docs_files)}")
    
    # 分析匹配情况
    print(f"\n🔍 匹配分析:")
    
    # 1. sidebar 中有但 version-0.72 中没有的
    missing_in_version_072 = sidebar_doc_ids - set(version_072_files.keys())
    if missing_in_version_072:
        print(f"\n❌ sidebar 中有但 version-0.72 目录中缺失的文档 ({len(missing_in_version_072)} 个):")
        for doc_id in sorted(missing_in_version_072):
            print(f"  {doc_id}")
    
    # 2. sidebar 中有但 docs 中没有的
    missing_in_docs = sidebar_doc_ids - set(docs_files.keys())
    if missing_in_docs:
        print(f"\n❌ sidebar 中有但 docs 目录中缺失的文档 ({len(missing_in_docs)} 个):")
        for doc_id in sorted(missing_in_docs):
            print(f"  {doc_id}")
    
    # 3. version-0.72 中有但 sidebar 中没有的
    extra_in_version_072 = set(version_072_files.keys()) - sidebar_doc_ids
    if extra_in_version_072:
        print(f"\n⚠️  version-0.72 目录中有但 sidebar 中没有的文档 ({len(extra_in_version_072)} 个):")
        for doc_id in sorted(extra_in_version_072):
            print(f"  {doc_id} -> {version_072_files[doc_id]}")
    
    # 4. docs 中有但 sidebar 中没有的
    extra_in_docs = set(docs_files.keys()) - sidebar_doc_ids
    if extra_in_docs:
        print(f"\n⚠️  docs 目录中有但 sidebar 中没有的文档 ({len(extra_in_docs)} 个):")
        for doc_id in sorted(extra_in_docs):
            print(f"  {doc_id} -> {docs_files[doc_id]}")
    
    # 5. 完全匹配的文档
    matched_docs = sidebar_doc_ids & set(version_072_files.keys()) & set(docs_files.keys())
    print(f"\n✅ 完全匹配的文档数量: {len(matched_docs)}")
    
    # 总结
    print(f"\n📊 总结:")
    print(f"  sidebar 定义的文档: {len(sidebar_doc_ids)}")
    print(f"  version-0.72 中的文档: {len(version_072_files)}")
    print(f"  docs 中的文档: {len(docs_files)}")
    print(f"  完全匹配的文档: {len(matched_docs)}")
    print(f"  version-0.72 中缺失: {len(missing_in_version_072)}")
    print(f"  docs 中缺失: {len(missing_in_docs)}")
    print(f"  version-0.72 中多余: {len(extra_in_version_072)}")
    print(f"  docs 中多余: {len(extra_in_docs)}")
    
    if (len(missing_in_version_072) == 0 and len(missing_in_docs) == 0 and 
        len(extra_in_version_072) == 0 and len(extra_in_docs) == 0):
        print(f"\n🎉 完美匹配！所有文件都正确对应。")
    else:
        print(f"\n⚠️  发现不匹配的情况，需要进一步处理。")

if __name__ == "__main__":
    main()
